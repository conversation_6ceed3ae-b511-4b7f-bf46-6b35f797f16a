# ===========================================
# 生产环境配置模板 - 前端应用
# ===========================================

# API 配置（生产环境 - 需要更新为实际的后端URL）
VITE_API_BASE_URL=https://inventory-ai-backend-xxx.onrender.com/api
VITE_SOCKET_URL=https://inventory-ai-backend-xxx.onrender.com

# 应用配置
VITE_APP_TITLE=库存管理系统 | HubGoodFood
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=production

# 功能开关（生产环境）
VITE_ENABLE_AI_CHAT=true
VITE_ENABLE_BARCODE_SCANNER=true
VITE_ENABLE_FILE_UPLOAD=true
VITE_ENABLE_PWA=true

# UI 配置
VITE_PRIMARY_COLOR=#2C7AFF
VITE_ENABLE_DARK_MODE=true
VITE_DEFAULT_LOCALE=zh-CN

# 调试配置（生产环境关闭）
VITE_DEBUG=false
VITE_SHOW_DEV_TOOLS=false
VITE_ENABLE_PERFORMANCE_MONITORING=true

# 构建配置（生产环境优化）
VITE_BUILD_OUTPUT_DIR=dist
VITE_CODE_SPLITTING=true
VITE_MINIFY=true
VITE_SOURCE_MAP=false
VITE_HMR=false

# 第三方服务配置（生产环境）
VITE_SENTRY_DSN=REPLACE_WITH_PRODUCTION_SENTRY_DSN
VITE_GOOGLE_ANALYTICS_ID=REPLACE_WITH_GA_ID

# 安全配置
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true

# 性能配置
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_IMAGE_OPTIMIZATION=true
VITE_CACHE_STRATEGY=aggressive

# CDN配置（可选）
VITE_CDN_URL=https://cdn.your-domain.com
VITE_STATIC_URL=https://static.your-domain.com
