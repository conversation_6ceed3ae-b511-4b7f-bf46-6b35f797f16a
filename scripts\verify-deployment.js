#!/usr/bin/env node

/**
 * 部署验证脚本
 * 验证后端和前端服务是否正常部署
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 配置
const config = {
  backend: {
    url: process.env.BACKEND_URL || 'https://inventory-ai-backend-xxx.onrender.com',
    timeout: 30000
  },
  frontend: {
    url: process.env.FRONTEND_URL || 'https://your-vercel-domain.vercel.app',
    timeout: 10000
  }
};

// 健康检查
async function checkHealth(url, timeout = 30000) {
  const startTime = performance.now();
  
  try {
    const response = await axios.get(`${url}/health`, {
      timeout,
      headers: {
        'User-Agent': 'Deployment-Verification-Script'
      }
    });
    
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);
    
    return {
      success: true,
      status: response.status,
      data: response.data,
      responseTime
    };
  } catch (error) {
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);
    
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      responseTime
    };
  }
}

// 测试API端点
async function testApiEndpoints(baseUrl) {
  const endpoints = [
    { path: '/api/auth/health', method: 'GET', description: '认证服务健康检查' },
    { path: '/api/products', method: 'GET', description: '产品列表API' },
    { path: '/api/inventory', method: 'GET', description: '库存API' }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      const startTime = performance.now();
      const response = await axios({
        method: endpoint.method,
        url: `${baseUrl}${endpoint.path}`,
        timeout: 10000,
        validateStatus: (status) => status < 500 // 接受4xx状态码
      });
      
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      results.push({
        ...endpoint,
        success: true,
        status: response.status,
        responseTime
      });
      
    } catch (error) {
      results.push({
        ...endpoint,
        success: false,
        error: error.message,
        status: error.response?.status || 'TIMEOUT'
      });
    }
  }
  
  return results;
}

// 测试WebSocket连接
async function testWebSocket(url) {
  return new Promise((resolve) => {
    try {
      const WebSocket = require('ws');
      const ws = new WebSocket(`${url.replace('https://', 'wss://').replace('http://', 'ws://')}/socket.io/?EIO=4&transport=websocket`);
      
      const timeout = setTimeout(() => {
        ws.close();
        resolve({
          success: false,
          error: 'WebSocket连接超时'
        });
      }, 10000);
      
      ws.on('open', () => {
        clearTimeout(timeout);
        ws.close();
        resolve({
          success: true,
          message: 'WebSocket连接成功'
        });
      });
      
      ws.on('error', (error) => {
        clearTimeout(timeout);
        resolve({
          success: false,
          error: error.message
        });
      });
      
    } catch (error) {
      resolve({
        success: false,
        error: error.message
      });
    }
  });
}

// 验证前端应用
async function verifyFrontend(url) {
  try {
    const response = await axios.get(url, {
      timeout: config.frontend.timeout,
      headers: {
        'User-Agent': 'Deployment-Verification-Script'
      }
    });
    
    const isReactApp = response.data.includes('react') || 
                      response.data.includes('React') ||
                      response.data.includes('root');
    
    return {
      success: true,
      status: response.status,
      isReactApp,
      size: response.data.length
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status
    };
  }
}

// 主验证函数
async function verifyDeployment() {
  log('🔍 开始部署验证...', 'blue');
  log('=' * 50, 'blue');
  
  const results = {
    backend: {},
    frontend: {},
    overall: { success: true, issues: [] }
  };
  
  // 验证后端
  log('\n🖥️  验证后端服务...', 'blue');
  log(`URL: ${config.backend.url}`, 'blue');
  
  // 健康检查
  const healthCheck = await checkHealth(config.backend.url, config.backend.timeout);
  results.backend.health = healthCheck;
  
  if (healthCheck.success) {
    log(`✅ 健康检查通过 (${healthCheck.responseTime}ms)`, 'green');
    log(`   状态: ${healthCheck.data.status}`, 'green');
    log(`   环境: ${healthCheck.data.environment}`, 'green');
    log(`   运行时间: ${Math.round(healthCheck.data.uptime)}秒`, 'green');
  } else {
    log(`❌ 健康检查失败: ${healthCheck.error}`, 'red');
    results.overall.success = false;
    results.overall.issues.push('后端健康检查失败');
  }
  
  // API端点测试
  if (healthCheck.success) {
    log('\n🔌 测试API端点...', 'blue');
    const apiResults = await testApiEndpoints(config.backend.url);
    results.backend.api = apiResults;
    
    apiResults.forEach(result => {
      if (result.success) {
        log(`✅ ${result.description}: ${result.status} (${result.responseTime}ms)`, 'green');
      } else {
        log(`❌ ${result.description}: ${result.error}`, 'red');
      }
    });
  }
  
  // WebSocket测试
  if (healthCheck.success) {
    log('\n🔗 测试WebSocket连接...', 'blue');
    const wsResult = await testWebSocket(config.backend.url);
    results.backend.websocket = wsResult;
    
    if (wsResult.success) {
      log(`✅ ${wsResult.message}`, 'green');
    } else {
      log(`⚠️  WebSocket测试失败: ${wsResult.error}`, 'yellow');
      results.overall.issues.push('WebSocket连接问题');
    }
  }
  
  // 验证前端
  log('\n🌐 验证前端应用...', 'blue');
  log(`URL: ${config.frontend.url}`, 'blue');
  
  const frontendResult = await verifyFrontend(config.frontend.url);
  results.frontend = frontendResult;
  
  if (frontendResult.success) {
    log(`✅ 前端应用访问正常`, 'green');
    log(`   状态码: ${frontendResult.status}`, 'green');
    log(`   页面大小: ${Math.round(frontendResult.size / 1024)}KB`, 'green');
    log(`   React应用: ${frontendResult.isReactApp ? '是' : '否'}`, 'green');
  } else {
    log(`❌ 前端应用访问失败: ${frontendResult.error}`, 'red');
    results.overall.success = false;
    results.overall.issues.push('前端应用无法访问');
  }
  
  // 生成报告
  log('\n📊 部署验证报告', 'blue');
  log('=' * 30, 'blue');
  
  if (results.overall.success) {
    log('🎉 部署验证通过！', 'green');
  } else {
    log('❌ 部署验证失败', 'red');
    log('\n发现的问题:', 'red');
    results.overall.issues.forEach(issue => {
      log(`  - ${issue}`, 'red');
    });
  }
  
  // 保存详细报告
  const reportPath = 'deployment-verification-report.json';
  require('fs').writeFileSync(reportPath, JSON.stringify(results, null, 2));
  log(`\n📄 详细报告已保存: ${reportPath}`, 'blue');
  
  return results.overall.success;
}

// 执行验证
if (require.main === module) {
  verifyDeployment()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`💥 验证过程发生错误: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { verifyDeployment, checkHealth, testApiEndpoints };
