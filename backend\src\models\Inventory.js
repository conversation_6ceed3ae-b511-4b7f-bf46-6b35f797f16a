const mongoose = require('mongoose');

const inventorySchema = new mongoose.Schema({
  product_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, '产品ID是必需的'],
    unique: true,
    index: true
  },
  current_stock: {
    type: Number,
    required: [true, '当前库存是必需的'],
    min: [0, '库存不能为负数'],
    default: 0
  },
  reserved_stock: {
    type: Number,
    min: [0, '预留库存不能为负数'],
    default: 0
  },
  available_stock: {
    type: Number,
    min: [0, '可用库存不能为负数'],
    default: 0
  },
  location: {
    type: String,
    trim: true,
    maxlength: [50, '存储位置不能超过50个字符']
  },
  last_stock_in: {
    type: Date,
    index: true
  },
  last_stock_out: {
    type: Date,
    index: true
  },
  last_count_date: {
    type: Date,
    index: true
  },
  last_count_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  cost_per_unit: {
    type: Number,
    min: [0, '单位成本不能为负数'],
    default: 0
  },
  total_value: {
    type: Number,
    min: [0, '总价值不能为负数'],
    default: 0
  },
  reorder_point: {
    type: Number,
    min: [0, '补货点不能为负数'],
    default: 0
  },
  max_stock: {
    type: Number,
    min: [0, '最大库存不能为负数']
  },
  is_active: {
    type: Boolean,
    default: true,
    index: true
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, '备注不能超过500个字符']
  },
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 虚拟字段：可用库存
inventorySchema.virtual('available_quantity').get(function() {
  return this.current_stock - this.reserved_stock;
});

// 检查是否需要补货
inventorySchema.methods.needsRestock = function() {
  return this.current_stock <= this.reorder_point;
};

// 检查是否库存过多
inventorySchema.methods.isOverstocked = function() {
  return this.max_stock && this.current_stock > this.max_stock;
};

// 更新可用库存
inventorySchema.methods.updateAvailableStock = function() {
  this.available_stock = Math.max(0, this.current_stock - this.reserved_stock);
  return this.available_stock;
};

// 计算总价值
inventorySchema.methods.calculateTotalValue = function() {
  this.total_value = this.current_stock * this.cost_per_unit;
  return this.total_value;
};

// 入库操作
inventorySchema.methods.stockIn = function(quantity, costPerUnit, userId) {
  if (quantity <= 0) {
    throw new Error('入库数量必须大于0');
  }
  
  // 更新加权平均成本
  const totalCost = (this.current_stock * this.cost_per_unit) + (quantity * costPerUnit);
  const totalQuantity = this.current_stock + quantity;
  
  this.current_stock = totalQuantity;
  this.cost_per_unit = totalCost / totalQuantity;
  this.last_stock_in = new Date();
  this.updated_by = userId;
  
  this.updateAvailableStock();
  this.calculateTotalValue();
  
  return this;
};

// 出库操作
inventorySchema.methods.stockOut = function(quantity, userId) {
  if (quantity <= 0) {
    throw new Error('出库数量必须大于0');
  }
  
  if (quantity > this.available_stock) {
    throw new Error('出库数量不能超过可用库存');
  }
  
  this.current_stock -= quantity;
  this.last_stock_out = new Date();
  this.updated_by = userId;
  
  this.updateAvailableStock();
  this.calculateTotalValue();
  
  return this;
};

// 预留库存
inventorySchema.methods.reserveStock = function(quantity, userId) {
  if (quantity <= 0) {
    throw new Error('预留数量必须大于0');
  }
  
  if (quantity > this.available_stock) {
    throw new Error('预留数量不能超过可用库存');
  }
  
  this.reserved_stock += quantity;
  this.updated_by = userId;
  
  this.updateAvailableStock();
  
  return this;
};

// 释放预留库存
inventorySchema.methods.releaseReservedStock = function(quantity, userId) {
  if (quantity <= 0) {
    throw new Error('释放数量必须大于0');
  }
  
  if (quantity > this.reserved_stock) {
    throw new Error('释放数量不能超过预留库存');
  }
  
  this.reserved_stock -= quantity;
  this.updated_by = userId;
  
  this.updateAvailableStock();
  
  return this;
};

// 复合索引
inventorySchema.index({ current_stock: 1, reorder_point: 1 });
inventorySchema.index({ is_active: 1, current_stock: 1 });

// 中间件：保存前更新计算字段
inventorySchema.pre('save', function(next) {
  this.updateAvailableStock();
  this.calculateTotalValue();
  next();
});

module.exports = mongoose.model('Inventory', inventorySchema);
