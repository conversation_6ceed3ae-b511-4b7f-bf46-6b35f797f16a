services:
  - type: static_site
    name: inventory-ai-frontend
    buildCommand: |
      cd frontend
      npm install --no-optional
      npm run build:prod
    staticPublishPath: frontend/dist
    pullRequestPreviewsEnabled: false
    envVars:
      - key: NODE_ENV
        value: production
      - key: VITE_API_BASE_URL
        value: https://inventory-ai-backend.onrender.com/api
      - key: VITE_SOCKET_URL
        value: https://inventory-ai-backend.onrender.com
      - key: VITE_APP_TITLE
        value: 库存管理系统 | HubGoodFood
      - key: VITE_APP_VERSION
        value: 1.0.0
      - key: VITE_NODE_ENV
        value: production
      - key: VITE_ENABLE_AI_CHAT
        value: true
      - key: VITE_ENABLE_BARCODE_SCANNER
        value: true
      - key: VITE_ENABLE_FILE_UPLOAD
        value: true
      - key: VITE_ENABLE_PWA
        value: true
      - key: VITE_PRIMARY_COLOR
        value: "#2C7AFF"
      - key: VITE_ENABLE_DARK_MODE
        value: true
      - key: VITE_DEFAULT_LOCALE
        value: zh-CN
      - key: VITE_DEBUG
        value: false
      - key: VITE_SHOW_DEV_TOOLS
        value: false
      - key: VITE_ENABLE_PERFORMANCE_MONITORING
        value: true
      - key: VITE_BUILD_OUTPUT_DIR
        value: dist
      - key: VITE_CODE_SPLITTING
        value: true
      - key: VITE_MINIFY
        value: true
      - key: VITE_SOURCE_MAP
        value: false
      - key: VITE_HMR
        value: false
      - key: VITE_ENABLE_CSP
        value: true
      - key: VITE_ENABLE_HTTPS_ONLY
        value: true
      - key: VITE_ENABLE_LAZY_LOADING
        value: true
      - key: VITE_ENABLE_IMAGE_OPTIMIZATION
        value: true
      - key: VITE_CACHE_STRATEGY
        value: aggressive
    headers:
      - name: X-Content-Type-Options
        value: nosniff
      - name: X-Frame-Options
        value: DENY
      - name: X-XSS-Protection
        value: 1; mode=block
      - name: Referrer-Policy
        value: strict-origin-when-cross-origin
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
