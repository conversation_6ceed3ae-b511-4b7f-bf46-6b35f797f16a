services:
  - type: web
    name: inventory-backend
    env: node
    plan: starter
    buildCommand: npm ci
    startCommand: node dist/server.js
    envVars:
      - key: MONGO_URI
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: AWS_ACCESS_KEY
        sync: false
      - key: AWS_SECRET_KEY
        sync: false
      - key: S3_BUCKET
        sync: false
      - key: LLM_PROVIDER
        sync: false
      - key: LLM_API_KEY
        sync: false