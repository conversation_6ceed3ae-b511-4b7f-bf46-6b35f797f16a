services:
  - type: web
    name: inventory-ai-backend
    env: node
    plan: starter
    region: singapore
    buildCommand: |
      cd backend
      npm ci --only=production
      npm run test
    startCommand: |
      cd backend
      npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 4000
      - key: MONGO_URI
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: AWS_ACCESS_KEY_ID
        sync: false
      - key: AWS_SECRET_ACCESS_KEY
        sync: false
      - key: AWS_REGION
        value: us-east-1
      - key: S3_BUCKET
        sync: false
      - key: DEEPSEEK_API_KEY
        sync: false
      - key: DEEPSEEK_BASE_URL
        value: https://llm.chutes.ai/v1
      - key: DEEPSEEK_MODEL
        value: deepseek-ai/DeepSeek-V3-0324
      - key: AI_MOCK_MODE
        value: false
      - key: FRONTEND_URL
        sync: false
      - key: CORS_ORIGIN
        sync: false
      - key: RATE_LIMIT_WINDOW_MS
        value: 900000
      - key: RATE_LIMIT_MAX_REQUESTS
        value: 200
      - key: LOG_LEVEL
        value: warn
      - key: ENABLE_HTTPS_REDIRECT
        value: true
      - key: TRUST_PROXY
        value: true