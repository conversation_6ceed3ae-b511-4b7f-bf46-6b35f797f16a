# ===========================================
# 生产环境配置模板 - 后端服务
# ===========================================
# 注意：此文件包含敏感信息，请勿提交到版本控制系统

# 服务器配置
PORT=4000
NODE_ENV=production

# 数据库配置（生产环境）
MONGO_URI=mongodb+srv://prod_user:<EMAIL>/inventory_ai_prod?retryWrites=true&w=majority

# JWT 认证配置（生产环境 - 强密码）
JWT_SECRET=REPLACE_WITH_STRONG_32_CHAR_SECRET_KEY_FOR_PRODUCTION_USE_ONLY
JWT_EXPIRES_IN=7d

# AWS S3 配置（生产环境）
AWS_ACCESS_KEY_ID=REPLACE_WITH_PRODUCTION_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=REPLACE_WITH_PRODUCTION_AWS_SECRET_KEY
AWS_REGION=us-east-1
S3_BUCKET=inventory-assets-prod

# DeepSeek AI 配置（生产环境）
DEEPSEEK_API_KEY=REPLACE_WITH_PRODUCTION_DEEPSEEK_API_KEY
DEEPSEEK_BASE_URL=https://llm.chutes.ai/v1
DEEPSEEK_MODEL=deepseek-ai/DeepSeek-V3-0324
AI_MOCK_MODE=false

# 前端配置（生产环境域名）
FRONTEND_URL=https://your-production-domain.com
CORS_ORIGIN=https://your-production-domain.com

# 邮件配置（生产环境）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=REPLACE_WITH_APP_PASSWORD

# 文件上传配置（生产环境优化）
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf

# 日志配置（生产环境）
LOG_LEVEL=warn
LOG_FILE=logs/production.log

# 缓存配置（生产环境 - 可选）
REDIS_URL=redis://production-redis-server:6379

# 安全配置（生产环境加强）
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200
ENABLE_HTTPS_REDIRECT=true
TRUST_PROXY=true
SESSION_SECURE=true

# 退货政策配置文件路径
REFUND_POLICY_PATH=../config/refund_policy.yaml

# 监控和健康检查
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# 数据库连接池配置（生产环境优化）
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=2
DB_MAX_IDLE_TIME=30000

# API版本控制
API_VERSION=v1
API_PREFIX=/api

# 错误处理配置
SHOW_STACK_TRACE=false
DETAILED_ERRORS=false

# 性能配置
ENABLE_COMPRESSION=true
ENABLE_ETAG=true
CACHE_MAX_AGE=3600
