#!/bin/bash

# ===========================================
# Render前端构建脚本
# 解决Rollup平台依赖问题
# ===========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}"
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️  $1${NC}"
}

# 清理函数
cleanup() {
    log "清理构建环境..."
    
    # 删除node_modules和lock文件
    if [ -d "node_modules" ]; then
        rm -rf node_modules
        log "删除 node_modules"
    fi
    
    if [ -f "package-lock.json" ]; then
        rm -f package-lock.json
        log "删除 package-lock.json"
    fi
    
    # 清理npm缓存
    npm cache clean --force
    log "清理 npm 缓存"
}

# 安装依赖
install_dependencies() {
    log "安装依赖包..."
    
    # 设置npm配置
    npm config set target_platform linux
    npm config set target_arch x64
    npm config set rebuild true
    
    # 安装依赖
    npm install --verbose
    
    # 验证关键依赖
    if [ ! -d "node_modules/@rollup" ]; then
        error "Rollup 依赖安装失败"
        
        # 尝试手动安装Rollup
        log "尝试手动安装 Rollup..."
        npm install @rollup/rollup-linux-x64-gnu --save-dev --verbose
    fi
    
    success "依赖安装完成"
}

# 构建项目
build_project() {
    log "开始构建项目..."
    
    # 设置环境变量
    export NODE_ENV=production
    export VITE_NODE_ENV=production
    
    # 运行构建
    npm run build:prod
    
    # 验证构建结果
    if [ -d "dist" ]; then
        success "构建成功"
        
        # 显示构建结果
        log "构建结果:"
        ls -la dist/
        
        # 检查关键文件
        if [ -f "dist/index.html" ]; then
            success "index.html 存在"
        else
            error "index.html 不存在"
            exit 1
        fi
        
    else
        error "构建失败 - dist 目录不存在"
        exit 1
    fi
}

# 主函数
main() {
    log "🚀 开始 Render 前端构建"
    log "=" * 50
    
    # 检查当前目录
    if [ ! -f "package.json" ]; then
        error "package.json 不存在，请确保在正确的目录中运行"
        exit 1
    fi
    
    # 显示环境信息
    log "环境信息:"
    log "  Node.js: $(node --version)"
    log "  npm: $(npm --version)"
    log "  平台: $(uname -s)"
    log "  架构: $(uname -m)"
    
    # 执行构建步骤
    cleanup
    install_dependencies
    build_project
    
    success "🎉 构建完成！"
    
    log "📋 构建摘要:"
    log "  输出目录: dist/"
    log "  文件数量: $(find dist -type f | wc -l)"
    log "  总大小: $(du -sh dist/ | cut -f1)"
}

# 错误处理
trap 'error "构建过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
