{"name": "inventory-ai-frontend", "version": "1.0.0", "description": "仓库库存管理及AI客服系统 - 前端界面", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "NODE_ENV=production vite build --mode production", "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist/stats.html", "preview": "vite preview", "preview:prod": "vite preview --mode production", "serve": "node server.js", "serve:dev": "NODE_ENV=development node server.js", "start": "npm run serve", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "deploy:render": "npm run build:prod && echo 'Ready for Render deployment'", "health-check": "curl -f http://localhost:3000/health || exit 1"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@hookform/resolvers": "^3.3.2", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "lucide-react": "^0.294.0", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-image-crop": "^11.0.5", "react-qr-scanner": "^1.0.0-alpha.11", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "yup": "^1.4.0", "zustand": "^4.4.7", "compression": "^1.7.4", "express": "^4.18.2", "helmet": "^7.1.0", "http-proxy-middleware": "^2.0.6"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}